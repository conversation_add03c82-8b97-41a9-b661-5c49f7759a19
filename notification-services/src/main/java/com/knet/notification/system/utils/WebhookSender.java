package com.knet.notification.system.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.notification.system.config.WebhookConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/15 10:05
 * @description: Webhook发送工具
 */
@Slf4j
@Component
public class WebhookSender {

    @Resource
    private WebhookConfig webhookConfig;

    /**
     * 发送APP_PUSH消息到Slack webhook
     * 通过Redis确保同一个父订单只能发送一次消息
     *
     * @param parentOrderId 父订单号
     * @param userAccount   用户账号
     * @param purchaseQty   子订单数量
     * @param totalPrice    订单总金额
     * @param purchaseTime  购买时间
     * @return 是否发送成功
     */
    public boolean sendAppPushMessage(String parentOrderId, String userAccount, Integer purchaseQty,
                                      String totalPrice, String purchaseTime) {
        // 使用Redis确保同一个父订单只能发送一次消息
        String redisKey = String.format("WEBHOOK_SENT:%s", parentOrderId);
        if (!RedisCacheUtil.setIfAbsent(redisKey, "SENT", 3600)) {
            log.info("父订单号{}的webhook消息已发送过，跳过发送", parentOrderId);
            return true;
        }
        try {
            // 构建Slack消息格式
            Map<String, Object> message = new HashMap<>(2);
            Map<String, Object> block = new HashMap<>(2);
            Map<String, Object> text = new HashMap<>(2);
            block.put("type", "section");
            text.put("type", "mrkdwn");
            text.put("text", String.format("*New B2B Sales Order*\nB2B Order : %s\nUSER ACCOUNT: %s\nPURCHASE QTY : %d\nTOTAL PRICE: %s\nPURCHASE TIME: %s",
                    parentOrderId, userAccount, purchaseQty, totalPrice, purchaseTime));
            block.put("text", text);
            message.put("blocks", List.of(block));
            String jsonBody = JSONUtil.toJsonStr(message);
            log.info("发送APP_PUSH消息到webhook: parentOrderId={}, userAccount={}, purchaseQty={}, totalPrice={}, purchaseTime={}",
                    parentOrderId, userAccount, purchaseQty, totalPrice, purchaseTime);
            String response = HttpUtil.post(webhookConfig.getAppPushUrl(), jsonBody);
            log.info("APP_PUSH消息发送成功: parentOrderId={}, response={}", parentOrderId, response);
            return true;
        } catch (Exception e) {
            log.error("发送APP_PUSH消息异常: parentOrderId={}, error={}", parentOrderId, e.getMessage(), e);
            RedisCacheUtil.del(redisKey);
            return false;
        }
    }
}
